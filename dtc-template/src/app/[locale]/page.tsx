"use client";

import { useUser } from "@/hooks/useUser";
import { logout } from "@/Services/authService";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";

export default function HomePage() {
  const { user, authUser, loading, isAuthenticated } = useUser();
  const router = useRouter();
  const locale = useLocale();

  const handleLogout = async () => {
    try {
      await logout();
      router.push(`/${locale}/login`);
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    router.push(`/${locale}/login`);
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold text-gray-900">
              DTC Portal
            </h1>
            <div className="flex items-center gap-4">
              {user && (
                <div className="flex items-center gap-3">
                  {user.photoURL && (
                    <img
                      src={user.photoURL}
                      alt={user.displayName || "User"}
                      className="w-8 h-8 rounded-full"
                    />
                  )}
                  <span className="text-sm text-gray-700">
                    {user.displayName || user.email}
                  </span>
                </div>
              )}
              <button
                onClick={handleLogout}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Welcome to DTC Portal
              </h2>
              
              {user && (
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">User Information</h3>
                    <dl className="mt-2 space-y-2">
                      <div>
                        <dt className="text-sm font-medium text-gray-900">Name:</dt>
                        <dd className="text-sm text-gray-600">{user.displayName || "Not provided"}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-900">Email:</dt>
                        <dd className="text-sm text-gray-600">{user.email}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-900">Sign-in Method:</dt>
                        <dd className="text-sm text-gray-600 capitalize">{user.provider}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-900">Email Verified:</dt>
                        <dd className="text-sm text-gray-600">
                          {user.emailVerified ? (
                            <span className="text-green-600">✓ Verified</span>
                          ) : (
                            <span className="text-yellow-600">⚠ Not verified</span>
                          )}
                        </dd>
                      </div>
                      {user.createdAt && (
                        <div>
                          <dt className="text-sm font-medium text-gray-900">Member Since:</dt>
                          <dd className="text-sm text-gray-600">
                            {new Date(user.createdAt.seconds * 1000).toLocaleDateString()}
                          </dd>
                        </div>
                      )}
                    </dl>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
