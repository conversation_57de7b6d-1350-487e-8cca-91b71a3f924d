"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useLocale } from "next-intl";

import { loginSchema, type LoginFormData } from "@/lib/validations/auth";
import { dtcAssets } from "@/lib/assets";
import { LoginField } from "./LoginField";
import BrandButton from "@/components/ui/BrandButton";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { googleSignIn } from "@/Services/authService";
import { createOrUpdateUserProfile } from "@/Services/userService";

interface LoginFormProps {
  onSubmit?: (data: LoginFormData) => Promise<void>;
  onGoogleSignIn?: () => Promise<void>;
  className?: string;
}

export function LoginForm({ onSubmit, onGoogleSignIn, className }: LoginFormProps) {
  const locale = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const t = useTranslations("auth.login");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const rememberMe = watch("rememberMe");

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      setLoginError(null);

      if (onSubmit) {
        await onSubmit(data);
      } else {
        // Default behavior - simulate login
        await new Promise(resolve => setTimeout(resolve, 1500));
        console.log("Login data:", data);
      }
    } catch {
      setLoginError("auth.login.errors.loginFailed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setLoginError(null);

      if (onGoogleSignIn) {
        await onGoogleSignIn();
      } else {
        // Default Google sign-in behavior
        const result = await googleSignIn();
        await createOrUpdateUserProfile(result.user, 'google');
        console.log("Google sign-in successful:", result.user);
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      setLoginError("auth.login.errors.googleSignInFailed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("w-full space-y-8", className)}>
      {/* Logo */}
      <div className="text-center">
        <div className="inline-block p-4">
          <Image
            src={dtcAssets.logoBlack}
            alt="DTC Logo"
            width={80}
            height={80}
            className="mx-auto"
            priority
          />
        </div>
      </div>

      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-charcoal font-display">
          {t("title")}
        </h1>
        <p className="text-grey">
          {t("subtitle")}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Global Error */}
        {loginError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 text-center">
              {t(loginError)}
            </p>
          </div>
        )}

        {/* Email Field */}
        <LoginField
          id="email"
          label={t("email.label")}
          placeholder={t("email.placeholder")}
          type="email"
          error={errors.email?.message}
          {...register("email")}
        />

        {/* Password Field */}
        <LoginField
          id="password"
          label={t("password.label")}
          placeholder={t("password.placeholder")}
          type="password"
          showPasswordToggle
          error={errors.password?.message}
          {...register("password")}
        />

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rememberMe"
              checked={rememberMe}
              onCheckedChange={(checked) => setValue("rememberMe", !!checked)}
            />
            <Label 
              htmlFor="rememberMe" 
              className="text-sm text-charcoal cursor-pointer"
            >
              {t("rememberMe")}
            </Label>
          </div>
          
          <Link
            href={`/${locale}/forgot-password`}
            className="text-sm text-primary hover:text-primary-deep transition-colors"
          >
            {t("forgotPassword")}
          </Link>
        </div>

        {/* Submit Button */}
        <BrandButton
          type="submit"
          disabled={isLoading}
          className="w-full h-12 text-base font-semibold"
        >
          {isLoading ? t("signInLoading") : t("signIn")}
        </BrandButton>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-200" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-gray-500">Or continue with</span>
          </div>
        </div>

        {/* Google Sign-In Button */}
        <button
          type="button"
          onClick={handleGoogleSignIn}
          disabled={isLoading}
          className="w-full h-12 px-4 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3"
        >
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {isLoading ? "Signing in..." : "Sign in with Google"}
        </button>

        {/* Sign Up Link */}
        <div className="text-center text-sm">
          <span className="text-grey">{t("noAccount")} </span>
          <Link
            href={`/${locale}/signup`}
            className="text-primary hover:text-primary-deep font-medium transition-colors"
          >
            {t("signUp")}
          </Link>
        </div>
      </form>
    </div>
  );
}
