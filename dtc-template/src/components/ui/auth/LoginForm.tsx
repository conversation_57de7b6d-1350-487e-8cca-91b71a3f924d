"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useLocale } from "next-intl";
import { Eye, EyeOff, Mail, Lock, Sparkles, ArrowRight } from "lucide-react";

import { loginSchema, type LoginFormData } from "@/lib/validations/auth";
import { dtcAssets } from "@/lib/assets";
import { LoginField } from "./LoginField";
import BrandButton from "@/components/ui/BrandButton";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { googleSignIn } from "@/Services/authService";
import { createOrUpdateUserProfile } from "@/Services/userService";
import { toast } from "sonner";

interface LoginFormProps {
  onSubmit?: (data: LoginFormData) => Promise<void>;
  onGoogleSignIn?: () => Promise<void>;
  className?: string;
}

export function LoginForm({ onSubmit, onGoogleSignIn, className }: LoginFormProps) {
  const locale = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const t = useTranslations("auth.login");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const rememberMe = watch("rememberMe");

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      setLoginError(null);

      if (onSubmit) {
        await onSubmit(data);
      } else {
        // Default behavior - simulate login
        await new Promise(resolve => setTimeout(resolve, 1500));
        console.log("Login data:", data);
      }
    } catch {
      setLoginError("auth.login.errors.loginFailed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setLoginError(null);

      if (onGoogleSignIn) {
        await onGoogleSignIn();
      } else {
        // Default Google sign-in behavior
        const result = await googleSignIn();
        const userProfile = await createOrUpdateUserProfile(result.user, 'google');
        toast.success(`Signed in successfully as ${userProfile.displayName || userProfile.email}!`);
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      setLoginError("auth.login.errors.googleSignInFailed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("w-full max-w-md space-y-8", className)}>
      {/* Futuristic Header with Animated Elements */}
      <div className="text-center space-y-6 relative">
        {/* Animated Background Glow */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-32 h-32 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 rounded-full blur-3xl animate-pulse"></div>
        </div>

        {/* Logo with Glow Effect */}
        <div className="flex justify-center mb-8 relative">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-primary to-accent rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
            <div className="relative bg-white/90 backdrop-blur-sm rounded-full p-4 border border-white/20 shadow-2xl">
              <Image
                src={dtcAssets.logoBlack}
                alt="DTC Logo"
                width={64}
                height={64}
                className="h-16 w-auto relative z-10"
                priority
              />
            </div>
          </div>
        </div>

        {/* Enhanced Typography */}
        <div className="space-y-3">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-charcoal via-primary to-charcoal bg-clip-text text-transparent font-display">
            {t("title")}
          </h1>
          <p className="text-grey/80 text-sm font-medium">
            {t("subtitle")}
          </p>
        </div>
      </div>

      {/* Futuristic Form Container */}
      <div className="relative animate-float">
        {/* Form Background with Glass Effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-white/60 to-white/40 backdrop-blur-xl rounded-3xl border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-500"></div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="relative z-10 p-8 space-y-8">
          {/* Global Error with Enhanced Styling */}
          {loginError && (
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-red-50 to-red-100/50 border border-red-200/50 p-4 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-red-600/5"></div>
              <p className="relative text-sm text-red-700 text-center font-medium">
                {t(loginError)}
              </p>
            </div>
          )}

          {/* Futuristic Email Field */}
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-semibold text-charcoal/90 mb-3">
              {t("email.label")}
            </label>
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl blur-sm opacity-0 group-focus-within:opacity-100 group-hover:opacity-50 transition-opacity duration-300"></div>
              <div className="relative flex items-center">
                <Mail className="absolute left-4 h-5 w-5 text-grey/60 z-10 transition-colors group-focus-within:text-primary" />
                <input
                  id="email"
                  type="email"
                  placeholder={t("email.placeholder")}
                  className={cn(
                    "w-full pl-12 pr-4 py-4 bg-white/70 backdrop-blur-sm border border-grey/20 rounded-2xl",
                    "text-charcoal placeholder:text-grey/60 font-medium",
                    "focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/50",
                    "transition-all duration-300 hover:bg-white/80",
                    "shadow-lg hover:shadow-xl focus:shadow-2xl",
                    errors.email && "border-red-300 focus:border-red-400 focus:ring-red-200"
                  )}
                  {...register("email")}
                  onFocus={() => setFocusedField("email")}
                  onBlur={() => setFocusedField(null)}
                />
              </div>
            </div>
            {errors.email && (
              <p className="text-sm text-red-600 font-medium ml-1">{t(errors.email.message)}</p>
            )}
          </div>

          {/* Futuristic Password Field */}
          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-semibold text-charcoal/90 mb-3">
              {t("password.label")}
            </label>
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl blur-sm opacity-0 group-focus-within:opacity-100 group-hover:opacity-50 transition-opacity duration-300"></div>
              <div className="relative flex items-center">
                <Lock className="absolute left-4 h-5 w-5 text-grey/60 z-10 transition-colors group-focus-within:text-primary" />
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder={t("password.placeholder")}
                  className={cn(
                    "w-full pl-12 pr-12 py-4 bg-white/70 backdrop-blur-sm border border-grey/20 rounded-2xl",
                    "text-charcoal placeholder:text-grey/60 font-medium",
                    "focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/50",
                    "transition-all duration-300 hover:bg-white/80",
                    "shadow-lg hover:shadow-xl focus:shadow-2xl",
                    errors.password && "border-red-300 focus:border-red-400 focus:ring-red-200"
                  )}
                  {...register("password")}
                  onFocus={() => setFocusedField("password")}
                  onBlur={() => setFocusedField(null)}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 h-5 w-5 text-grey/60 hover:text-primary transition-colors z-10"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>
            {errors.password && (
              <p className="text-sm text-red-600 font-medium ml-1">{t(errors.password.message)}</p>
            )}
          </div>

          {/* Enhanced Remember Me & Forgot Password */}
          <div className="flex items-center justify-between py-2">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Checkbox
                  id="rememberMe"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setValue("rememberMe", !!checked)}
                  className="rounded-lg border-2 border-grey/30 data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-primary data-[state=checked]:to-accent data-[state=checked]:border-primary shadow-lg"
                />
              </div>
              <Label
                htmlFor="rememberMe"
                className="text-sm text-charcoal/80 cursor-pointer font-medium hover:text-charcoal transition-colors"
              >
                {t("rememberMe")}
              </Label>
            </div>

            <Link
              href={`/${locale}/forgot-password`}
              className="text-sm text-primary hover:text-primary-deep transition-all duration-300 font-medium hover:underline underline-offset-4"
            >
              {t("forgotPassword")}
            </Link>
          </div>

          {/* Futuristic Submit Button */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-primary via-accent to-primary rounded-2xl blur-lg opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
            <button
              type="submit"
              disabled={isLoading}
              className={cn(
                "relative w-full py-4 px-6 bg-gradient-to-r from-primary to-accent text-white font-bold text-base rounded-2xl",
                "shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.02]",
                "disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none",
                "flex items-center justify-center gap-3",
                "border border-white/20 backdrop-blur-sm"
              )}
            >
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>{t("signInLoading")}</span>
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5" />
                  <span>{t("signIn")}</span>
                  <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                </>
              )}
            </button>
          </div>

          {/* Futuristic Divider */}
          <div className="relative py-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full h-px bg-gradient-to-r from-transparent via-grey/30 to-transparent"></div>
            </div>
            <div className="relative flex justify-center">
              <span className="bg-gradient-to-r from-white via-white to-white px-6 py-2 text-xs font-semibold text-grey/70 uppercase tracking-wider backdrop-blur-sm rounded-full border border-grey/20">
                Or continue with
              </span>
            </div>
          </div>

          {/* Premium Google Sign-In Button */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-red-500/20 via-yellow-500/20 to-green-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
            <button
              type="button"
              onClick={handleGoogleSignIn}
              disabled={isLoading}
              className={cn(
                "relative w-full py-4 px-6 bg-white/90 backdrop-blur-sm border border-grey/20 rounded-2xl",
                "text-charcoal font-semibold text-base shadow-xl hover:shadow-2xl",
                "transition-all duration-300 transform hover:scale-[1.02]",
                "disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none",
                "flex items-center justify-center gap-4",
                "hover:bg-white/95 focus:outline-none focus:ring-2 focus:ring-primary/30"
              )}
            >
              <div className="relative">
                <svg className="w-6 h-6" viewBox="0 0 24 24">
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
              </div>
              <span className="bg-gradient-to-r from-charcoal to-grey bg-clip-text text-transparent">
                {isLoading ? "Signing in..." : "Sign in with Google"}
              </span>
            </button>
          </div>

          {/* Enhanced Sign Up Link */}
          <div className="text-center pt-4">
            <p className="text-sm text-grey/80 font-medium">
              <span>{t("noAccount")} </span>
              <Link
                href={`/${locale}/signup`}
                className="text-primary hover:text-primary-deep font-semibold transition-all duration-300 hover:underline underline-offset-4 bg-gradient-to-r from-primary to-accent bg-clip-text hover:text-transparent"
              >
                {t("signUp")}
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
