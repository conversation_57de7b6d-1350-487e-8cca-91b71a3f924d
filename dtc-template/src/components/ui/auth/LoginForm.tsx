"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useLocale } from "next-intl";

import { loginSchema, type LoginFormData } from "@/lib/validations/auth";
import { dtcAssets } from "@/lib/assets";
import { LoginField } from "./LoginField";
import BrandButton from "@/components/ui/BrandButton";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface LoginFormProps {
  onSubmit?: (data: LoginFormData) => Promise<void>;
  className?: string;
}

export function LoginForm({ onSubmit, className }: LoginFormProps) {
  const locale = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const t = useTranslations("auth.login");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const rememberMe = watch("rememberMe");

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      setLoginError(null);
      
      if (onSubmit) {
        await onSubmit(data);
      } else {
        // Default behavior - simulate login
        await new Promise(resolve => setTimeout(resolve, 1500));
        console.log("Login data:", data);
      }
    } catch {
      setLoginError("auth.login.errors.loginFailed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("w-full space-y-8", className)}>
      {/* Logo */}
      <div className="text-center">
        <div className="inline-block p-4">
          <Image
            src={dtcAssets.logoBlack}
            alt="DTC Logo"
            width={80}
            height={80}
            className="mx-auto"
            priority
          />
        </div>
      </div>

      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-charcoal font-display">
          {t("title")}
        </h1>
        <p className="text-grey">
          {t("subtitle")}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Global Error */}
        {loginError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 text-center">
              {t(loginError)}
            </p>
          </div>
        )}

        {/* Email Field */}
        <LoginField
          id="email"
          label={t("email.label")}
          placeholder={t("email.placeholder")}
          type="email"
          error={errors.email?.message}
          {...register("email")}
        />

        {/* Password Field */}
        <LoginField
          id="password"
          label={t("password.label")}
          placeholder={t("password.placeholder")}
          type="password"
          showPasswordToggle
          error={errors.password?.message}
          {...register("password")}
        />

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rememberMe"
              checked={rememberMe}
              onCheckedChange={(checked) => setValue("rememberMe", !!checked)}
            />
            <Label 
              htmlFor="rememberMe" 
              className="text-sm text-charcoal cursor-pointer"
            >
              {t("rememberMe")}
            </Label>
          </div>
          
          <Link
            href={`/${locale}/forgot-password`}
            className="text-sm text-primary hover:text-primary-deep transition-colors"
          >
            {t("forgotPassword")}
          </Link>
        </div>

        {/* Submit Button */}
        <BrandButton
          type="submit"
          disabled={isLoading}
          className="w-full h-12 text-base font-semibold"
        >
          {isLoading ? t("signInLoading") : t("signIn")}
        </BrandButton>

        {/* Sign Up Link */}
        <div className="text-center text-sm">
          <span className="text-grey">{t("noAccount")} </span>
          <Link
            href={`/${locale}/signup`}
            className="text-primary hover:text-primary-deep font-medium transition-colors"
          >
            {t("signUp")}
          </Link>
        </div>
      </form>
    </div>
  );
}
