"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import { dtcAssets } from "@/lib/assets";
import { cn } from "@/lib/utils";
import { LanguageSwitcher } from "./LanguageSwitcher";

interface AuthLayoutProps {
  children: React.ReactNode;
  heroImage?: keyof typeof dtcAssets;
  className?: string;
}

export function AuthLayout({
  children,
  heroImage = "hero1",
  className
}: AuthLayoutProps) {
  const t = useTranslations("app");

  return (
    <div className={cn("min-h-screen flex relative overflow-hidden", className)}>
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        {/* Floating Orbs */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-accent/15 to-primary/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      {/* Left side - Enhanced Hero Section (70%) */}
      <div className="hidden lg:flex lg:w-[70%] relative overflow-hidden">
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-primary/10 to-accent/20 z-10" />

        {/* Hero Image */}
        <Image
          src={dtcAssets[heroImage]}
          alt="DTC Hero"
          fill
          className="object-cover"
          priority
          sizes="70vw"
        />

        {/* Glass Morphism Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20 backdrop-blur-[1px] z-15"></div>

        {/* Enhanced Overlay Content */}
        <div className="relative z-20 flex flex-col justify-end p-16 text-white">
          <div className="max-w-lg">
            {/* Floating Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 text-sm font-medium mb-6">
              <div className="w-2 h-2 bg-accent rounded-full mr-2 animate-pulse"></div>
              Digital Transformation Center
            </div>

            <h1 className="text-5xl font-bold mb-6 font-display leading-tight">
              {t("title")}
            </h1>
            <p className="text-xl opacity-90 leading-relaxed font-medium">
              {t("heroSubtitle")}
            </p>

            {/* Decorative Elements */}
            <div className="flex items-center gap-4 mt-8">
              <div className="w-12 h-px bg-gradient-to-r from-white to-transparent"></div>
              <div className="w-2 h-2 bg-white rounded-full opacity-60"></div>
              <div className="w-8 h-px bg-gradient-to-r from-white/60 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Enhanced Auth Form Section (30%) */}
      <div className="w-full lg:w-[30%] flex flex-col relative z-10">
        {/* Enhanced Language Switcher */}
        <div className="flex justify-end p-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg">
            <LanguageSwitcher />
          </div>
        </div>

        {/* Form Container with Enhanced Styling */}
        <div className="flex-1 flex items-center justify-center p-8 lg:p-12 pt-0">
          <div className="w-full max-w-md relative">
            {/* Background Glow for Form Area */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-white/10 rounded-3xl blur-xl -z-10"></div>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
